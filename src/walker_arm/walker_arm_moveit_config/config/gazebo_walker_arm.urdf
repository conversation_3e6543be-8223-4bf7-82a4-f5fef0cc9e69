<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /ros_ws/src/hiwin_ros/hiwin_description/urdf/ra.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="ra610_1476">
    <!-- create link fixed to the "world" -->
    <link name="world" />
    <link name="base_link">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/base.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/base.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_1">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_1.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_1.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_2">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_2.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_2.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_3">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_3.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_3.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_4">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_4.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_4.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_5">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_5.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_5.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <link name="link_6">
        <visual>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/visual/link_6.stl" scale="1 1 1" />
            </geometry>
            <material name="LightGrey">
                <color rgba="0.7 0.7 0.7 1.0" />
            </material>
        </visual>
        <collision>
            <origin rpy="0 0 0" xyz="0 0 0" />
            <geometry>
                <mesh filename="package://hiwin_description/meshes/ra610_1476/collision/link_6.stl" scale="1 1 1" />
            </geometry>
        </collision>
        <inertial>
            <mass value="0.1" />
            <origin xyz="0 0 0" rpy="0 0 0" />
            <inertia ixx="0.03" iyy="0.03" izz="0.03" ixy="0.0" ixz="0.0" iyz="0.0" />
        </inertial>
    </link>
    <!-- base_joint fixes base_link to the environment -->
    <joint name="base_joint" type="fixed">
        <origin rpy="0 0 0" xyz="0 0 0" />
        <parent link="world" />
        <child link="base_link" />
    </joint>
    <!-- joints - main serial chain -->
    <joint name="joint_1" type="revolute">
        <origin rpy="0 0 0" xyz="0 0 0.4485" />
        <parent link="base_link" />
        <child link="link_1" />
        <axis xyz="0 0 1" />
        <limit effort="888.345" lower="-2.9670597283903604" upper="2.9670597283903604" velocity="1.3438337108655538" />
    </joint>
    <joint name="joint_2" type="revolute">
        <origin rpy="0 0 0" xyz="0 0.14 0" />
        <parent link="link_1" />
        <child link="link_2" />
        <axis xyz="1 0 0" />
        <limit effort="829.997" lower="-2.6179938779914944" upper="1.6580627893946132" velocity="1.4383432898610469" />
    </joint>
    <joint name="joint_3" type="revolute">
        <origin rpy="0 0 0" xyz="0 0 0.64" />
        <parent link="link_2" />
        <child link="link_3" />
        <axis xyz="1 0 0" />
        <limit effort="392.127" lower="-1.4835298641951802" upper="3.2288591161895095" velocity="1.9147832690704591" />
    </joint>
    <joint name="joint_4" type="revolute">
        <origin rpy="0 0 0" xyz="0 0 0.16" />
        <parent link="link_3" />
        <child link="link_4" />
        <axis xyz="0 1 0" />
        <limit effort="51.597" lower="-3.3161255787892263" upper="3.3161255787892263" velocity="3.878505570366839" />
    </joint>
    <joint name="joint_5" type="revolute">
        <origin rpy="0 0 0" xyz="0 0.678 0" />
        <parent link="link_4" />
        <child link="link_5" />
        <axis xyz="1 0 0" />
        <limit effort="25.44" lower="-2.356194490192345" upper="2.356194490192345" velocity="3.9269908169872414" />
    </joint>
    <joint name="joint_6" type="revolute">
        <origin rpy="0 0 0" xyz="0 0.101 0" />
        <parent link="link_5" />
        <child link="link_6" />
        <axis xyz="0 1 0" />
        <limit effort="15.9" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586" />
    </joint>
    <!-- ROS-Industrial 'base' frame - base_link to HIWIN 'Base' Coordinates transform -->
    <link name="base" />
    <joint name="base_link-base_fixed_joint" type="fixed">
        <!-- Note the rotation over Z of pi radians - as base_link is REP-103
           aligned (i.e., has X+ forward, Y+ left and Z+ up), this is needed
           to correctly align 'base' with the 'Base' coordinate system of
           the HIWIN controller.
      -->
        <origin rpy="0 0 0" xyz="0 0 0.4485" />
        <parent link="base_link" />
        <child link="base" />
    </joint>
    <!-- ROS-Industrial 'flange' frame - attachment point for EEF models -->
    <link name="flange" />
    <joint name="flange" type="fixed">
        <parent link="link_6" />
        <child link="flange" />
        <origin rpy="0 0 0" xyz="0 0 0" />
    </joint>
    <!-- ROS-Industrial 'tool0' frame - all-zeros tool frame -->
    <link name="tool0" />
    <joint name="flange-tool0" type="fixed">
        <!-- default toolframe - X+ left, Y+ up, Z+ front -->
        <origin rpy="0 0 0" xyz="0 0 0" />
        <parent link="flange" />
        <child link="tool0" />
    </joint>
    <transmission name="trans_joint_1">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_1">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_1_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <transmission name="trans_joint_2">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_2">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_2_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <transmission name="trans_joint_3">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_3">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_3_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <transmission name="trans_joint_4">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_4">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_4_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <transmission name="trans_joint_5">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_5">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_5_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <transmission name="trans_joint_6">
        <type>transmission_interface/SimpleTransmission</type>
        <joint name="joint_6">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
        </joint>
        <actuator name="joint_6_motor">
            <hardwareInterface>hardware_interface/EffortJointInterface</hardwareInterface>
            <mechanicalReduction>1</mechanicalReduction>
        </actuator>
    </transmission>
    <gazebo>
        <plugin name="gazebo_ros_control" filename="libgazebo_ros_control.so">
            <robotNamespace>/</robotNamespace>
        </plugin>
    </gazebo>
</robot>

